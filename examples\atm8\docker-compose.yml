services:
  mc:
    image: itzg/minecraft-server:java17
    ports:
      - "25565:25565"
    environment:
      EULA: "true"
      MODPACK_PLATFORM: AUTO_CURSEFORGE
      # Allocate API key from https://console.curseforge.com/
      # and set in .env file making sure to double up dollar signs, such as
      # CF_API_KEY=$$2a$$10$$....
      # Refer to https://docker-minecraft-server.readthedocs.io/en/latest/types-and-platforms/mod-platforms/auto-curseforge/#api-key
      CF_API_KEY: ${CF_API_KEY}
      CF_PAGE_URL: https://www.curseforge.com/minecraft/modpacks/all-the-mods-8
#      CF_FILENAME_MATCHER: "1.1.0"
      MEMORY: 4G
    volumes:
      - mc-data:/data
  # Since Dock<PERSON>'s default volume driver creates volumes owned by root, this
  # init container will change ownership to match final UID of mc service, above
  init-filebrowser:
    image: filebrowser/filebrowser
    entrypoint: sh -c
    command:
      - "chown -R 1000: /database"
    restart: no
    volumes:
      - filebrowser-db:/database
  filebrowser:
    image: filebrowser/filebrowser
    depends_on:
      init-filebrowser:
        condition: service_completed_successfully
    user: "1000:1000"
    environment:
      FB_DATABASE: /database/filebrowser.db
    volumes:
      # Default FB_ROOT is /srv
      # In this example, the left-side needs to be the same as /data volume of mc service
      - mc-data:/srv
      - filebrowser-db:/database
    ports:
      - "25580:80"

volumes:
  mc-data: {}
  filebrowser-db: {}