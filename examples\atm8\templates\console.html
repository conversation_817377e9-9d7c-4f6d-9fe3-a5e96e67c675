{% extends "base.html" %}

{% block title %}Console - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal"></i> Server Console
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleAutoRefresh()">
                        <i class="fas fa-play" id="autoRefreshIcon"></i> Auto-refresh
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="console-output" id="consoleOutput">
                    {{ logs if logs else "Loading console logs..." }}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-keyboard"></i> Send Command
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> Be careful when executing commands. Some commands can affect server performance or player experience.
                </div>
                <form id="commandForm">
                    <div class="input-group">
                        <span class="input-group-text">/</span>
                        <input type="text" class="form-control" id="commandInput" placeholder="Enter command (without /)" required>
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane"></i> Execute
                        </button>
                    </div>
                </form>
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Common commands:</strong>
                        <code>list</code>, <code>say &lt;message&gt;</code>, <code>tp &lt;player&gt; &lt;target&gt;</code>, 
                        <code>gamemode &lt;mode&gt; &lt;player&gt;</code>, <code>time set &lt;time&gt;</code>, 
                        <code>weather &lt;clear|rain|thunder&gt;</code>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock"></i> Quick Commands
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="executeQuickCommand('list')">
                        <i class="fas fa-users"></i> List Players
                    </button>
                    <button class="btn btn-outline-info" onclick="executeQuickCommand('time set day')">
                        <i class="fas fa-sun"></i> Set Time to Day
                    </button>
                    <button class="btn btn-outline-secondary" onclick="executeQuickCommand('time set night')">
                        <i class="fas fa-moon"></i> Set Time to Night
                    </button>
                    <button class="btn btn-outline-success" onclick="executeQuickCommand('weather clear')">
                        <i class="fas fa-cloud-sun"></i> Clear Weather
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Console Info
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Auto-refresh:</strong> <span id="autoRefreshStatus">Disabled</span></p>
                <p><strong>Last updated:</strong> <span id="lastUpdated">{{ moment().format('HH:mm:ss') if moment else 'Unknown' }}</span></p>
                <p><strong>Log lines:</strong> Showing last 100 lines</p>
                <hr>
                <small class="text-muted">
                    The console shows real-time server logs including player activity, 
                    server events, and command outputs. Use the refresh button to get 
                    the latest logs or enable auto-refresh for continuous updates.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let autoRefreshEnabled = false;
let autoRefreshInterval;

function refreshLogs() {
    fetch('/api/console?lines=100')
        .then(response => response.json())
        .then(data => {
            if (data.logs) {
                document.getElementById('consoleOutput').textContent = data.logs;
                // Scroll to bottom
                const consoleOutput = document.getElementById('consoleOutput');
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
                
                // Update last updated time
                document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();
            }
        })
        .catch(error => {
            console.error('Error refreshing logs:', error);
            document.getElementById('consoleOutput').textContent = 'Error loading logs: ' + error.message;
        });
}

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    const icon = document.getElementById('autoRefreshIcon');
    const status = document.getElementById('autoRefreshStatus');
    
    if (autoRefreshEnabled) {
        icon.className = 'fas fa-pause';
        status.textContent = 'Enabled (every 5 seconds)';
        autoRefreshInterval = setInterval(refreshLogs, 5000);
    } else {
        icon.className = 'fas fa-play';
        status.textContent = 'Disabled';
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }
}

function executeQuickCommand(command) {
    document.getElementById('commandInput').value = command;
    document.getElementById('commandForm').dispatchEvent(new Event('submit'));
}

// Handle command form submission
document.getElementById('commandForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const command = document.getElementById('commandInput').value.trim();
    
    if (!command) return;
    
    // For now, we'll just show the command in the console
    // In a real implementation, you'd send this to the server via RCON
    const consoleOutput = document.getElementById('consoleOutput');
    consoleOutput.textContent += '\n> /' + command + '\n[Command executed - check server logs for output]';
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
    
    // Clear the input
    document.getElementById('commandInput').value = '';
    
    // Refresh logs after a short delay to see the command output
    setTimeout(refreshLogs, 1000);
});

// Auto-scroll console to bottom on page load
document.addEventListener('DOMContentLoaded', function() {
    const consoleOutput = document.getElementById('consoleOutput');
    consoleOutput.scrollTop = consoleOutput.scrollHeight;
});
</script>
{% endblock %}
