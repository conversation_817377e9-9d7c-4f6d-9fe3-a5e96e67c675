{% extends "base.html" %}

{% block title %}Players - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> Online Players 
                    <span class="badge bg-primary">{{ online_players.online_count }}/{{ online_players.max_players }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if detailed_players %}
                <div class="row">
                    {% for player in detailed_players %}
                    <div class="col-md-6 mb-3">
                        <div class="card player-card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-user"></i> {{ player.name }}
                                    <span class="badge bg-success">Online</span>
                                </h6>
                                
                                {% if player.position %}
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt"></i> 
                                        Position: {{ "%.1f"|format(player.position.x) }}, 
                                        {{ "%.1f"|format(player.position.y) }}, 
                                        {{ "%.1f"|format(player.position.z) }}
                                    </small>
                                </p>
                                {% endif %}
                                
                                {% if player.health %}
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-heart"></i> Health: {{ player.health }}
                                    </small>
                                </p>
                                {% endif %}
                                
                                {% if player.gamemode is defined %}
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-gamepad"></i> 
                                        Mode: {{ {0: "Survival", 1: "Creative", 2: "Adventure", 3: "Spectator"}.get(player.gamemode, "Unknown") }}
                                    </small>
                                </p>
                                {% endif %}
                                
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal" onclick="setPlayerName('{{ player.name }}')">
                                        <i class="fas fa-sign-out-alt"></i> Kick
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal" onclick="setPlayerName('{{ player.name }}')">
                                        <i class="fas fa-ban"></i> Ban
                                    </button>
                                    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#teleportModal" onclick="setPlayerName('{{ player.name }}')">
                                        <i class="fas fa-location-arrow"></i> TP
                                    </button>
                                    <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal" onclick="setPlayerName('{{ player.name }}')">
                                        <i class="fas fa-crown"></i> OP
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No players currently online</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> Whitelist
                </h5>
            </div>
            <div class="card-body">
                {% if whitelist %}
                    {% for player in whitelist %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ player }}</span>
                        <button class="btn btn-sm btn-outline-danger">Remove</button>
                    </div>
                    {% endfor %}
                {% else %}
                <p class="text-muted">No whitelisted players</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ban"></i> Banned Players
                </h5>
            </div>
            <div class="card-body">
                {% if banlist %}
                    {% for player in banlist %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ player }}</span>
                        <form method="POST" action="{{ url_for('unban_player') }}" style="display: inline;">
                            <input type="hidden" name="player_name" value="{{ player }}">
                            <button type="submit" class="btn btn-sm btn-outline-success">Unban</button>
                        </form>
                    </div>
                    {% endfor %}
                {% else %}
                <p class="text-muted">No banned players</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#kickModal">
                        <i class="fas fa-sign-out-alt"></i> Kick Player
                    </button>
                    <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#banModal">
                        <i class="fas fa-ban"></i> Ban Player
                    </button>
                    <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#opModal">
                        <i class="fas fa-crown"></i> Give OP
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Kick Modal -->
<div class="modal fade" id="kickModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Kick Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('kick_player') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="kickPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="kickPlayerName" name="player_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="kickReason" class="form-label">Reason</label>
                        <input type="text" class="form-control" id="kickReason" name="reason" value="Kicked by admin">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Kick Player</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Ban Modal -->
<div class="modal fade" id="banModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ban Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('ban_player') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="banPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="banPlayerName" name="player_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="banReason" class="form-label">Reason</label>
                        <input type="text" class="form-control" id="banReason" name="reason" value="Banned by admin">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Ban Player</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Teleport Modal -->
<div class="modal fade" id="teleportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Teleport Player</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('teleport_player') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="tpPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="tpPlayerName" name="player_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="tpTarget" class="form-label">Target (player name or coordinates)</label>
                        <input type="text" class="form-control" id="tpTarget" name="target" placeholder="PlayerName or 0 64 0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Teleport</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OP Modal -->
<div class="modal fade" id="opModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Give Operator Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('op_player') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="opPlayerName" class="form-label">Player Name</label>
                        <input type="text" class="form-control" id="opPlayerName" name="player_name" required>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Operator status gives players full administrative privileges.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Give OP</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function setPlayerName(playerName) {
    document.getElementById('kickPlayerName').value = playerName;
    document.getElementById('banPlayerName').value = playerName;
    document.getElementById('tpPlayerName').value = playerName;
    document.getElementById('opPlayerName').value = playerName;
}
</script>
{% endblock %}
