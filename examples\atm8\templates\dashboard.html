{% extends "base.html" %}

{% block title %}Dashboard - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server"></i> Server Status
                </h5>
            </div>
            <div class="card-body">
                {% if status %}
                <div class="row">
                    <div class="col-md-6">
                        <h6>Server Status</h6>
                        <p class="status-{{ status.status }}">
                            <i class="fas fa-circle"></i> {{ status.status.title() }}
                        </p>
                        
                        <h6>Players Online</h6>
                        <p>
                            <span id="online-count">{{ status.players.online_count }}</span> / 
                            <span id="max-players">{{ status.players.max_players }}</span>
                        </p>
                        
                        {% if status.players.players %}
                        <h6>Currently Online</h6>
                        <div class="d-flex flex-wrap gap-2">
                            {% for player in status.players.players %}
                            <span class="badge bg-success">{{ player }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6>Last Updated</h6>
                        <p>{{ status.timestamp.split('T')[1].split('.')[0] }}</p>
                        
                        {% if status.tps and 'not available' not in status.tps %}
                        <h6>Server Performance</h6>
                        <p>{{ status.tps }}</p>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Unable to connect to server. Please check if the server is running.
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bullhorn"></i> Send Message to All Players
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('send_message') }}">
                    <div class="input-group">
                        <input type="text" class="form-control" name="message" placeholder="Enter message..." required>
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane"></i> Send
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history"></i> Recent Activity
                </h5>
            </div>
            <div class="card-body">
                {% if recent_activity %}
                    {% for activity in recent_activity %}
                    <div class="activity-item activity-{{ activity.action }}">
                        <small class="text-muted">{{ activity.time }}</small><br>
                        <strong>{{ activity.player }}</strong> {{ activity.action }} the game
                    </div>
                    {% endfor %}
                {% else %}
                <p class="text-muted">No recent activity</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('players') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> Manage Players
                    </a>
                    <a href="{{ url_for('console') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-terminal"></i> View Console
                    </a>
                    <button class="btn btn-outline-info" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh Status
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Server Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6>Server Address</h6>
                        <p><code>localhost:25565</code></p>
                    </div>
                    <div class="col-md-3">
                        <h6>Version</h6>
                        <p>Minecraft 1.21.4</p>
                    </div>
                    <div class="col-md-3">
                        <h6>Mode</h6>
                        <p>Offline (Cracked)</p>
                    </div>
                    <div class="col-md-3">
                        <h6>File Browser</h6>
                        <p><a href="http://localhost:25580" target="_blank">localhost:25580</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
